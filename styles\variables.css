:root {
/* Brand colors */
  --color-cyan: #40E0D0;
  --color-blue: #0A233C;
  --color-dark-cyan: #21756C;
  --color-dark-blue: #021329;
/* Neutral colors */
  --color-white: #FFFFFF;
  --color-black: #000000;
}

.theme-blue {
  --color-primary: var(--color-cyan);
  --color-secondary: var(--color-blue);
  --color-tertiary: var(--color-dark-cyan);
  --color-quaternary: var(--color-dark-blue);
  --color-bg: var(--color-secondary);
  --color-text: var(--color-white);
}

.theme-cyan {
  --color-primary: var(--color-blue);
  --color-secondary: var(--color-cyan);
  --color-tertiary: var(--color-dark-blue);
  --color-quaternary: var(--color-dark-cyan);
  --color-bg: var(--color-secondary);
  --color-text: var(--color-white);
}

.theme-white {
  --color-primary: var(--color-black);
  --color-secondary: var(--color-white);
  --color-tertiary: var(--color-cyan);
  --color-quaternary: var(--color-blue);
  --color-bg: var(--color-secondary);
  --color-text: var(--color-black);
}

/*
  --color-white-75: rgba(255, 255, 255, 0.75);  /* or #FFFFFFBF /
  --color-white-50: rgba(255, 255, 255, 0.50);  /* or #FFFFFF80 /
  --color-white-25: rgba(255, 255, 255, 0.25);  /* or #FFFFFF40 /
  --color-white-13: rgba(255, 255, 255, 0.13);  /* or #FFFFFF1F /
  --color-white-75: rgba(255, 255, 255, 0.75);  /* or #FFFFFFBF /
  --color-white-50: rgba(255, 255, 255, 0.50);  /* or #FFFFFF80 /
  --color-white-25: rgba(255, 255, 255, 0.25);  /* or #FFFFFF40 /
  --color-white-13: rgba(255, 255, 255, 0.13);  /* or #FFFFFF1F /
*/
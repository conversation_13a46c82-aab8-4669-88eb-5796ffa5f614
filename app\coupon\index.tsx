import React, { useRef, useEffect, useState, ComponentType, forwardRef, PropsWithoutRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Vibration,
  Animated,
  ActivityIndicator, // Import ActivityIndicator
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router"; // Import useLocalSearchParams
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js'; // Import your Supabase client

// Define the interface for the coupon data to ensure type safety
interface CouponData {
  coupon_id: number;
  discount: string;
  short_description: string;
  long_description: string;
  image: string; // URL for the coupon image (main image)
  qr: string; // URL for the QR code (if used later)
  special_offer: boolean;
  brand: {
    brand_id: number;
    displayed_name: string;
    logo: string; // URL for the brand logo
    link: string;
    color: string;
    savings_ft: number;
    discount: string; // Added 'discount' to the brand interface
    activation: number; // Added 'activation' to the brand interface
  };
}

const vibrationPattern = [1];
let vibrationInterval: number | null = null;

export default function CouponPage() {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const { couponId } = useLocalSearchParams(); // Get the couponId from route parameters
  const [coupon, setCoupon] = useState<CouponData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const scrollY = useRef(new Animated.Value(0)).current;

  const imageScale = scrollY.interpolate({
    inputRange: [-180, 0],
    outputRange: [1.5, 1],
    extrapolate: 'clamp',
  });

  const imageTranslateY = scrollY.interpolate({
    inputRange: [0, 360],
    outputRange: [0, 180],
    extrapolate: 'clamp',
  });

  useEffect(() => {
    async function fetchCouponDetails() {
      if (!couponId) {
        setIsLoading(false);
        setError("No coupon ID provided.");
        return;
      }

      setIsLoading(true);
      setError(null);
      try {
        const { data, error: supabaseError } = await supabase
          .from('coupon') // Make sure this table name is correct
          .select(`
            coupon_id,
            discount,
            short_description,
            long_description,
            image,
            qr,
            special_offer,
            brand (
              brand_id,
              displayed_name,
              logo,
              savings_ft,
              discount,
              activation,
              link,
              color
            )
          `)
          .eq('coupon_id', parseInt(couponId as string)) // Ensure couponId is an integer
          .single(); // Use .single() as we expect one record

        if (supabaseError) {
          console.error("Error fetching coupon details:", supabaseError);
          setError("Failed to load coupon details. Please try again.");
          setCoupon(null);
        } else if (data) {
          setCoupon(data as unknown as CouponData);
        } else {
          setError("Coupon not found.");
          setCoupon(null);
        }
      } catch (err) {
        console.error("Unexpected error fetching coupon details:", err);
        setError("An unexpected error occurred.");
        setCoupon(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCouponDetails();
  }, [couponId]); // Re-run effect if couponId changes

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>Loading coupon...</Text>
      </SafeAreaView>
    );
  }

  if (error || !coupon) {
    return (
      <SafeAreaView style={[styles.errorContainer, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error || "Coupon data not available."}</Text>
        <TouchableOpacity style={{ marginTop: 20 }} onPress={() => router.back()}>
          <Text style={{ color: theme.primary, fontSize: 16 }}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  // Determine the background color for the brand logo box
  const brandLogoBoxBackgroundColor = coupon.brand?.color || 'white';

  // Format savings_ft for display
  const formattedSavings = coupon.brand?.savings_ft
    ? new Intl.NumberFormat('hu-HU', { style: 'currency', currency: 'HUF', maximumFractionDigits: 0 }).format(coupon.brand.savings_ft)
    : 'N/A Ft'; // N/A for Not Available, or whatever default you prefer

  // Display activation count, with a fallback
  const displayActivation = coupon.brand?.activation !== undefined ? coupon.brand.activation.toString() : 'N/A';

  // Display discount from brand, with a fallback
  const displayBrandDiscount = coupon.brand?.discount !== undefined ? coupon.brand.discount.toString() : 'N/A';


  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
      {/* Fixed navigation header (not scrollable) */}
      <View style={[styles.navigationHeaderBg, { backgroundColor: theme.tertiary }]}>
        <View style={styles.navigationHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={28} color={theme.primary} />
          </TouchableOpacity>
          {/* Brand logo next to arrow with dynamic background color */}
          {coupon.brand && coupon.brand.logo ? (
            <View style={[styles.brandLogoBox, { backgroundColor: brandLogoBoxBackgroundColor }]}>
              <Image
                source={{ uri: coupon.brand.logo }}
                style={styles.brandLogo}
              />
            </View>
          ) : null}
        </View>
      </View>
      {/* Scrollable content starts here */}
      <Animated.ScrollView
        contentContainerStyle={styles.scrollContentContainer}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
      >
        <View style={styles.imageContainer}>
          {coupon.image ? (
            <Animated.Image
              source={{ uri: coupon.image }}
              style={[
                styles.topImage,
                {
                  transform: [
                    { scale: imageScale },
                    { translateY: imageTranslateY },
                  ],
                },
              ]}
            />
          ) : (
            // Fallback for no image, e.g., a colored view or placeholder
            <Animated.View
              style={[
                styles.topImage, // Use same dimensions as image
                {
                  backgroundColor: theme.tertiary, // A placeholder background color
                  transform: [
                    { scale: imageScale },
                    { translateY: imageTranslateY },
                  ],
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}
            >
              <Text style={{ color: theme.text, fontSize: 20 }}>No Image</Text>
            </Animated.View>
          )}
        </View>
        <View style={[styles.cyanBanner, { backgroundColor: theme.primary }]}>
          {/* Displaying discount from the coupon table for the main banner */}
          <Text style={[styles.cyanBannerText, { color: theme.text }]}>{coupon.discount}</Text>
        </View>
        <View style={[styles.card, { width: '92%', alignSelf: 'center', backgroundColor: theme.bg, borderColor: theme.primary }]}>
          <View style={styles.header}>
            <Text style={[styles.cardHeaderText, { color: theme.text }]}>Tudnivalók:</Text>
          </View>
          <Text style={[styles.cardText, { color: theme.text }]}>
            {/* Display long description directly */}
            {coupon.long_description}
          </Text>
        </View>
        <View style={{ width: '92%', alignSelf: 'center', marginTop: 16 }}>
          {/* Spórolás now dynamic */}
          <View style={{
            backgroundColor: theme.bg,
            borderRadius: 16,
            paddingVertical: 20,
            paddingHorizontal: 10,
            alignItems: "center",
            marginBottom: 0,
            borderWidth: 1.5,
            borderColor: theme.primary,
            position: "relative",
          }}>
            <Text style={{ color: theme.text, fontSize: 32, fontWeight: "bold", marginTop: 10 }}>{formattedSavings}</Text>
            <View style={{
              width: 40,
              height: 3,
              backgroundColor: theme.primary,
              borderRadius: 2,
              marginTop: 6,
              marginBottom: 2,
            }} />
            <Text style={{ color: theme.primary, fontSize: 14, marginBottom: 2 }}>Spórolás</Text>
          </View>
        </View>
        {/* Kedvezmény and Aktiválás sections */}
        <View style={{ width: '92%', alignSelf: 'center', marginTop: 8 }}>
          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <View style={{
              backgroundColor: theme.bg,
              borderRadius: 16,
              padding: 20,
              alignItems: "center",
              flex: 1,
              marginRight: 8,
              borderWidth: 1.5,
              borderColor: theme.primary,
            }}>
              <Text style={{ color: theme.text, fontSize: 22, fontWeight: "bold" }}>
                {displayBrandDiscount} {/* <--- Using discount from brand table */}
              </Text>
              <Text style={{ color: theme.primary, fontSize: 14 }}>Kedvezmény</Text>
            </View>
            <View style={{
              backgroundColor: theme.bg,
              borderRadius: 16,
              padding: 20,
              alignItems: "center",
              flex: 1,
              marginLeft: 8,
              borderWidth: 1.5,
              borderColor: theme.primary,
            }}>
              <Text style={{ color: theme.text, fontSize: 22, fontWeight: "bold" }}>{displayActivation}</Text>
              <Text style={{ color: theme.primary, fontSize: 14 }}>Aktiválás</Text>
            </View>
          </View>
        </View>
        {/* Márka megtekintése gomb - ScrollView-on belül! */}
        {coupon.brand && coupon.brand.link ? (
            <View style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 50,
                marginTop: 20,
            }}>
                <TouchableOpacity
                    style={[styles.brandButton, { backgroundColor: theme.secondary }]}
                    activeOpacity={0.85}
                    onPress={() => {
                        // Navigate to the brand's page, potentially passing brandId
                        router.push({ pathname: "/brand", params: { brandId: coupon.brand.brand_id.toString() } });
                    }}
                >
                    <Text style={[styles.brandButtonText, { color: theme.text }]}>Márka megtekintése</Text>
                    <Ionicons name="open-outline" size={22} color={theme.text} style={{ marginLeft: 8 }} />
                </TouchableOpacity>
            </View>
        ) : null}
        <View style={{ height: 64 }} />
      </Animated.ScrollView>
      {/* Fixed bottom button */}
      <View style={styles.fixedButtonContainer}>
        <TouchableOpacity
          style={[styles.fixedButton, { backgroundColor: theme.primary }]}
          activeOpacity={0.85}
          onPressIn={() => {
            Vibration.vibrate(vibrationPattern, true);
            vibrationInterval ??= setInterval(() => {
              Vibration.vibrate(vibrationPattern, true);
            }, 1);
          }}
          onPressOut={() => {
            Vibration.cancel();
            if (vibrationInterval) {
              clearInterval(vibrationInterval);
              vibrationInterval = null;
            }
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            <View style={{
              backgroundColor: theme.text,
              borderRadius: 999,
              padding: 0,
              marginRight: 10,
              alignItems: 'center',
              justifyContent: 'center',
              width: 50,
              height: 50,
            }}>
              <Ionicons name="arrow-forward" size={36} color={theme.primary} />
            </View>
            <Text style={[styles.fixedButtonText, { color: theme.text }]}>Felhasználom</Text>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

type AnimatedProps = {
  animate?: { rotate: number };
  transition?: { duration: number };
};

export function withRotate<P extends object = {}>(Component: ComponentType<P & AnimatedProps>) {
  return forwardRef<any, PropsWithoutRef<P>>((props, ref) => (
    <Component
      ref={ref}
      {...(props as P)}
      animate={{ rotate: 90 }}
      transition={{ duration: 2 }}
    />
  ));
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
  },
  card: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeaderText: {
    fontWeight: "bold",
    fontSize: 20,
  },
  cardText: {
    fontSize: 18,
    lineHeight: 28,
  },
  header: {
    alignItems: "flex-start",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  infoContainer: {
    flexDirection: "row",
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 16,
    fontWeight: "bold",
    width: 100,
    color: "#666",
  },
  infoValue: {
    fontSize: 16,
    flex: 1,
    color: "#333",
  },
  termsText: {
    fontSize: 14,
    color: "#888",
    fontStyle: "italic",
    marginTop: 16,
    lineHeight: 20,
  },
  navigationHeaderBg: {
    width: '100%',
    paddingTop: 0,
    paddingBottom: 0,
  },
  navigationHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  brandLogoBox: {
    borderRadius: 14,
    padding: 6,
    marginLeft: 2,
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    width: 48,
  },
  brandLogo: {
    width: 36,
    height: 36,
    resizeMode: 'contain',
  },
  imageContainer: {
    width: "100%",
    alignItems: "center",
    marginTop: 0,
    marginBottom: 0,
  },
  topImage: {
    width: "100%",
    height: 180,
    resizeMode: "cover",
    borderRadius: 0,
  },
  cyanBanner: {
    width: "92%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    marginTop: 10,
    marginBottom: 8,
    borderRadius: 999,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  cyanBannerText: {
    fontSize: 24,
    fontWeight: "bold",
    letterSpacing: 1,
  },
  scrollContentContainer: {
    padding: 0,
    paddingBottom: 32,
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingBottom: 18,
    zIndex: 10,
  },
  fixedButton: {
    width: '92%',
    borderRadius: 999,
    paddingVertical: 6,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  fixedButtonText: {
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  brandButton: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 18,
  },
  brandButtonText: {
    fontWeight: "bold",
    fontSize: 16,
    marginRight: 4,
  },
});
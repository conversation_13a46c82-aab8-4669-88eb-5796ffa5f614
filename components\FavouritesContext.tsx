import React, { createContext, useContext, useState } from "react";

type Favourite = { id: number; name: string; image: any };

type FavouritesContextType = {
  favourites: Favourite[];
  addFavourite: (item: Favourite) => void;
  removeFavourite: (id: number) => void;
  isFavourite: (id: number) => boolean;
};

const FavouritesContext = createContext<FavouritesContextType | undefined>(undefined);

export const FavouritesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [favourites, setFavourites] = useState<Favourite[]>([]);

  const addFavourite = (item: Favourite) => {
    setFavourites((prev) => (prev.some(fav => fav.id === item.id) ? prev : [...prev, item]));
  };

  const removeFavourite = (id: number) => {
    setFavourites((prev) => prev.filter(fav => fav.id !== id));
  };

  const isFavourite = (id: number) => favourites.some(fav => fav.id === id);

  return (
    <FavouritesContext.Provider value={{ favourites, addFavourite, removeFavourite, isFavourite }}>
      {children}
    </FavouritesContext.Provider>
  );
};

export const useFavourites = () => {
  const context = useContext(FavouritesContext);
  if (!context) throw new Error("useFavourites must be used within a FavouritesProvider");
  return context;
};

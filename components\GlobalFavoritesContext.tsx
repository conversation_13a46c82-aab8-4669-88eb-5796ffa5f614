import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useAuth } from '@/components/AuthContext';
import { addFavoriteBrand, removeFavoriteBrand, getFavoriteBrandIds } from '@/components/FavsContext';

interface GlobalFavoritesContextType {
  userFavoriteBrandIds: number[];
  isLoading: boolean;
  toggleFavorite: (brandId: number) => Promise<boolean>;
  refreshFavorites: () => Promise<void>;
}

const GlobalFavoritesContext = createContext<GlobalFavoritesContextType | undefined>(undefined);

export const GlobalFavoritesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { employeeId, isLoadingAuth } = useAuth();
  const [userFavoriteBrandIds, setUserFavoriteBrandIds] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch favorites when employeeId changes
  const refreshFavorites = useCallback(async () => {
    if (isLoadingAuth) {
      setIsLoading(true);
      return;
    }

    if (employeeId) {
      try {
        setIsLoading(true);
        const favorites = await getFavoriteBrandIds(employeeId);
        setUserFavoriteBrandIds(favorites);
      } catch (error) {
        console.error('Error fetching favorites:', error);
      } finally {
        setIsLoading(false);
      }
    } else {
      setUserFavoriteBrandIds([]);
      setIsLoading(false);
    }
  }, [employeeId, isLoadingAuth]);

  // Initial fetch and when auth state changes
  useEffect(() => {
    refreshFavorites();
  }, [refreshFavorites]);

  // Toggle favorite function that updates global state
  const toggleFavorite = useCallback(async (brandIdToToggle: number): Promise<boolean> => {
    if (!employeeId) {
      Alert.alert("Authentication Required", "Please log in to favorite brands.");
      return false;
    }

    const isCurrentlyFavorited = userFavoriteBrandIds.includes(brandIdToToggle);

    let success = false;
    if (isCurrentlyFavorited) {
      success = await removeFavoriteBrand(employeeId, brandIdToToggle);
      if (success) {
        setUserFavoriteBrandIds(prev => prev.filter(id => id !== brandIdToToggle));
      } else {
        Alert.alert("Error", "Could not remove brand from favorites. Please try again.");
      }
    } else {
      success = await addFavoriteBrand(employeeId, brandIdToToggle);
      if (success) {
        setUserFavoriteBrandIds(prev => [...prev, brandIdToToggle]);
      } else {
        Alert.alert("Error", "Could not add brand to favorites. It might already be favorited or another error occurred.");
      }
    }
    return success;
  }, [employeeId, userFavoriteBrandIds]);

  const value: GlobalFavoritesContextType = {
    userFavoriteBrandIds,
    isLoading,
    toggleFavorite,
    refreshFavorites,
  };

  return (
    <GlobalFavoritesContext.Provider value={value}>
      {children}
    </GlobalFavoritesContext.Provider>
  );
};

export const useGlobalFavorites = (): GlobalFavoritesContextType => {
  const context = useContext(GlobalFavoritesContext);
  if (context === undefined) {
    throw new Error('useGlobalFavorites must be used within a GlobalFavoritesProvider');
  }
  return context;
};

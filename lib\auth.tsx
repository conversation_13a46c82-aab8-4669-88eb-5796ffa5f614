import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from "expo-router";

type User = {
  id: string;
  email: string;
};

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock user storage
const mockUsers: { [email: string]: { password: string; id: string } } = {};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in from localStorage
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setIsLoading(false);
  }, []);

  const signIn = async (email: string, password: string) => {
    // Only allow admin/0000 for testing
    if (email === "admin" && password === "0000") {
      const id = "testadmin";
      const user = { id, email };
      setUser(user);
      localStorage.setItem('user', JSON.stringify(user));
    } else {
      throw new Error("Invalid credentials. Use admin / 0000.");
    }
  };

  const signUp = async (email: string, password: string) => {
    // Only allow admin/0000 for testing
    await signIn(email, password);
  };

  const signOut = () => {
    setUser(null);
    localStorage.removeItem('user');
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signUp, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

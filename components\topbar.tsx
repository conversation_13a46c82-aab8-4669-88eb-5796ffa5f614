import { Ionicons } from "@expo/vector-icons";
import { router, Tabs } from "expo-router";
import { Pressable, Text, StyleSheet, Platform, View, TextInput, TouchableOpacity } from "react-native";
import  { useState } from "react";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext"; // Import your useTheme hook

export default function TabLayout() {
  const logoutIcon = Platform.OS === "ios" ? "log-out-outline" : "log-out";
  const homeIcon = Platform.OS === "ios" ? "home-outline" : "home";
  const pricetagIcon = Platform.OS === "ios" ? "pricetag-outline" : "pricetag";

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  // Keresés állapot a fejlécben
  const [search, setSearch] = useState("");
  const [searchVisible, setSearchVisible] = useState(false);
  const router = useRouter();
  return (
    <View
     style={{
      flexDirection: "row",
      justifyContent: "space-around",
      alignItems: "center",
      padding: 10,
      backgroundColor: theme.tertiary,
      }}
    >
      <Text style={{color: theme.text, fontSize:32}}>Ever</Text>
      <Text style={{color: theme.primary, fontSize:32}}>Deal</Text>
      <Pressable
              onPress={() => router.replace("/")}
              style={({ pressed }) => ({
                opacity: pressed ? 0.5 : 1,
                marginRight: 10,
                marginLeft:"auto"
              })}
            >
              <Ionicons name={logoutIcon} size={36} color={theme.primary} />
            </Pressable>
    </View>
  );
}

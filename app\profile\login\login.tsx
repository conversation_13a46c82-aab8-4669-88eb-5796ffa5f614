import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext"; // Import your useTheme hook

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [aszfChecked, setAszfChecked] = useState(false);
  const [adatChecked, setAdatChecked] = useState(false);
  const router = useRouter();

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  const canContinue = email.length > 0 && aszfChecked && adatChecked;

  const handleLogin = () => {
    router.replace({
      pathname: "/profile/login/verification",
      params: { email },
    });
  };

  // Custom checkbox component
  function CustomCheckbox({
    value,
    onValueChange,
  }: Readonly<{
    value: boolean;
    onValueChange: (newValue: boolean) => void;
  }>) {
    return (
      <TouchableOpacity
        onPress={() => onValueChange(!value)}
        style={{
          width: 24,
          height: 24,
          borderWidth: 2,
          borderColor: value ? theme.primary : theme.text,
          backgroundColor: value ? theme.primary : "transparent",
          borderRadius: 6,
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {value && (
          <Text style={{ color: theme.text, fontSize: 18, fontWeight: "bold", lineHeight: 20 }}>✓</Text>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <View style={styles.innerContainer}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <Text style={[styles.title, { color: theme.text }]}>Ever</Text>
            <Text
              style={[styles.title, { color: theme.primary, marginLeft: 2 }]}
            >
              Deal
            </Text>
          </View>
          <Text style={[styles.subtitle, { color: theme.text }]}>Sign in to your account</Text>

          <View style={styles.inputContainer}>
            <Text style={[styles.label, { color: theme.text }]}>Email</Text>
            <TextInput
              style={[styles.input, { color: "#000" }]}
              placeholder="Type your email"
              placeholderTextColor="#aaa"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
              editable={!loading}
            />
          </View>

          {/* Checkboxes */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <CustomCheckbox value={aszfChecked} onValueChange={setAszfChecked} />
            <Text style={{ color: theme.text, marginLeft: 8 }}>ÁSZF</Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 16,
            }}
          >
            <CustomCheckbox value={adatChecked} onValueChange={setAdatChecked} />
            <Text style={{ color: theme.text, marginLeft: 8 }}>
              Adataim felhasználása
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.button,
              canContinue
                ? { backgroundColor: theme.primary }
                : { backgroundColor: theme.secondary },
            ]}
            onPress={handleLogin}
            disabled={!canContinue}
          >
            <Text style={[styles.buttonText, { color: theme.text }]}>
              Continue
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
      {/* Back Button at the bottom */}
      <View style={styles.backButtonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          activeOpacity={0.85}
          onPress={() => router.replace('/')}
        >
          <Text style={[styles.backButtonText, { color: theme.text }]}>Back</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: "#0A233C",
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardAvoid: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    padding: 24,
    justifyContent: "center",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
    //color: "#fff",
  },
  subtitle: {
    fontSize: 18,
    //color: "#fff",
    marginBottom: 32,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
    //color: "#fff",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: "#f9f9f9",
  },
  button: {
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },
  buttonText: {
    //color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  // --- Back Button Styles ---
  backButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    alignItems: 'center',
    paddingBottom: 18,
    zIndex: 10,
  },
  backButton: {
    backgroundColor: '#21756C',
    borderRadius: 18,
    paddingVertical: 12,
    paddingHorizontal: 32,
    marginBottom: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  backButtonText: {
    //color: '#fff',
    fontSize: 17,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
});

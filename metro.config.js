const { getDefaultConfig } = require('@expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  stream: require.resolve('./empty.js'),
  http: require.resolve('./empty.js'),
  https: require.resolve('./empty.js'),
  util: require.resolve('./empty.js'),
  crypto: require.resolve('./empty.js'),
  net: require.resolve('./empty.js'),
  tls: require.resolve('./empty.js'),
  url: require.resolve('./empty.js'),
  zlib: require.resolve('./empty.js'),
  fs: require.resolve('./empty.js'),
  path: require.resolve('./empty.js'),
  os: require.resolve('./empty.js'),
  ws: require.resolve('./empty.js'),
  '@supabase/realtime-js': require.resolve('./empty.js'),
};

module.exports = config;
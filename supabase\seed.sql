-- Seed script for EverDeal database
-- Run this after schema.sql to populate with test data

-- IMPORTANT: This seed script is for development/testing only
-- Run this script with the admin role to bypass RLS: 
-- psql -U postgres -h localhost -d postgres -f seed.sql

-- First, temporarily disable the foreign key constraint for profiles
BEGIN;

-- For testing, create dummy users directly in auth.users
-- This bypasses normal auth flow but works for development seeding
INSERT INTO auth.users (id, email, created_at) VALUES
  ('d0d8489c-c332-4f7a-96e8-af73481df6af', '<EMAIL>', now()),
  ('c7d0f364-ae9c-4c24-b355-e9cf5adcafe2', '<EMAIL>', now()),
  ('7d3d95a5-b4c2-4f5c-b065-21ab5f13dd49', '<EMAIL>', now());

-- Now insert the profiles (foreign key constraint will be satisfied)
INSERT INTO profiles (id, username, avatar_url, website, updated_at)
VALUES
  ('d0d8489c-c332-4f7a-96e8-af73481df6af', 'johndoe', 'https://example.com/avatars/johndoe.jpg', 'https://johndoe.com', now()),
  ('c7d0f364-ae9c-4c24-b355-e9cf5adcafe2', 'janedoe', 'https://example.com/avatars/janedoe.jpg', 'https://janedoe.com', now()),
  ('7d3d95a5-b4c2-4f5c-b065-21ab5f13dd49', 'bobsmith', 'https://example.com/avatars/bobsmith.jpg', 'https://bobsmith.io', now());

-- Populate discounts table
INSERT INTO discounts (code, percentage, description, active, created_at, expires_at)
VALUES
  ('WELCOME10', 10, 'Welcome discount for new users', true, now(), now() + interval '30 days'),
  ('SUMMER25', 25, 'Summer sale discount', true, now(), now() + interval '15 days'),
  ('FLASH50', 50, 'Flash sale - 50% off', true, now(), now() + interval '2 days'),
  ('HOLIDAY20', 20, 'Holiday season discount', false, now() - interval '60 days', now() - interval '30 days'),
  ('LOYALTY15', 15, 'Loyal customer discount', true, now(), now() + interval '90 days');

-- Populate discount_transactions table
INSERT INTO discount_transactions (user_id, discount_id, applied_at, order_amount, discount_amount)
VALUES
  ('d0d8489c-c332-4f7a-96e8-af73481df6af', (SELECT id FROM discounts WHERE code = 'WELCOME10'), now() - interval '20 days', 100.00, 10.00),
  ('c7d0f364-ae9c-4c24-b355-e9cf5adcafe2', (SELECT id FROM discounts WHERE code = 'SUMMER25'), now() - interval '10 days', 80.00, 20.00),
  ('7d3d95a5-b4c2-4f5c-b065-21ab5f13dd49', (SELECT id FROM discounts WHERE code = 'FLASH50'), now() - interval '5 days', 120.00, 60.00),
  ('d0d8489c-c332-4f7a-96e8-af73481df6af', (SELECT id FROM discounts WHERE code = 'LOYALTY15'), now() - interval '2 days', 200.00, 30.00);

-- Populate storage.objects with dummy avatar entries
-- Note: In a real application, you would upload actual files through the Supabase storage API
-- This is just to simulate the database records
INSERT INTO storage.objects (id, bucket_id, name, owner, created_at, updated_at, last_accessed_at, metadata)
VALUES
  (gen_random_uuid(), 'avatars', 'johndoe.jpg', 'd0d8489c-c332-4f7a-96e8-af73481df6af', now(), now(), now(), '{"size": 24560, "mimetype": "image/jpeg"}' ),
  (gen_random_uuid(), 'avatars', 'janedoe.jpg', 'c7d0f364-ae9c-4c24-b355-e9cf5adcafe2', now(), now(), now(), '{"size": 18720, "mimetype": "image/jpeg"}' ),
  (gen_random_uuid(), 'avatars', 'bobsmith.jpg', '7d3d95a5-b4c2-4f5c-b065-21ab5f13dd49', now(), now(), now(), '{"size": 31450, "mimetype": "image/jpeg"}');

COMMIT;

--- a/c:\Users\<USER>\Desktop\Bme\App\App_demo\app\coupon\coupon.tsx
+++ b/c:\Users\<USER>\Desktop\Bme\App\App_demo\app\coupon\coupon.tsx
@@ -108,7 +108,7 @@
               borderRadius: 6,
               backgroundColor: "#40E0D0",
             }} />
-            <Text style={{ color: "#fff", fontSize: 32, fontWeight: "bold", marginTop: 10 }}>9.678.333 Ft</Text>
+            <Text style={{ color: "#fff", fontSize: 32, fontWeight: "bold", marginTop: 10 }}>9.678.333 Ft</Text>
             {/* Alsó vonal */}
             <View style={{
               width: 40,
import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from "react-native";
import { Ionicons } from "@expo/vector-icons";

const allCategories = [
  "Elektronika",
  "Egészségügy",
  "Divat",
  "<PERSON>ul<PERSON>",
  "Élelmiszer",
  "Kozmetikum",
  "Közlekedés",
  "Sport",
  "Szabadidő",
  "Bevásárlás",
  "Kultúra",
  "Szórakozás",
];

export default function ProfilePage() {
  const [editing, setEditing] = useState(false);
  const [profile, setProfile] = useState({
    email: "<EMAIL>",
    age: "33",
    gender: "<PERSON><PERSON><PERSON><PERSON>",
    address: "8000 Székesfehérvár, Budai út 66.",
    company: "EverDeal",
    position: "Senior Full Stack Developer",
  });
  const [selectedCategories, setSelectedCategories] = useState<string[]>(["Olvasás", "Sport"]);
  const [editInterests, setEditInterests] = useState(false);
  const [activeFavourites, setActiveFavourites] = useState<number[]>([1, 2, 3]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.emoji}>🧑‍💻</Text>
        <Text style={styles.name}>Tszigeti A. Hegyrol</Text>
      </View>

      <View style={styles.savingsBox}>
        <Text style={styles.savingsAmount}>25.756 Ft</Text>
        <Text style={styles.savingsLabel}>Megtakarítás</Text>
      </View>

      <View style={styles.statsRow}>
        <View style={styles.statBox}>
          <Text style={styles.statNumber}>45</Text>
          <Text style={styles.statLabel}>Aktivált Kupon</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statNumber}>{activeFavourites.length}</Text>
          <Text style={styles.statLabel}>Kedvenc Üzlet</Text>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionRow}>
          <Text style={styles.sectionTitle}>Érdeklődési körök</Text>
          <TouchableOpacity onPress={() => setEditInterests((e) => !e)}>
            <Ionicons name={editInterests ? "checkmark-outline" : "create-outline"} size={18} color="#38d9a9" style={{ marginLeft: 8 }} />
          </TouchableOpacity>
        </View>
        {editInterests ? (
          <View style={styles.categoriesGrid}>
            {allCategories.map((cat) => {
              const selected = selectedCategories.includes(cat);
              return (
                <TouchableOpacity
                  key={cat}
                  style={[
                    styles.categoryChip,
                    selected ? styles.categoryChipSelected : styles.categoryChipUnselected,
                  ]}
                  onPress={() => {
                    setSelectedCategories((prev) =>
                      selected ? prev.filter((c) => c !== cat) : [...prev, cat]
                    );
                  }}
                >
                  <Ionicons
                    name={selected ? "remove" : "add"}
                    size={16}
                    color={selected ? "#fff" : "#0A233C"}
                    style={{ marginRight: 4 }}
                  />
                  <Text style={[styles.categoryText, selected && { color: "#fff" }]}>
                    {cat}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ) : (
          <View style={styles.interestsRow}>
            {selectedCategories.map((cat) => (
              <View key={cat} style={styles.interestChip}>
                <Text style={styles.interestText}>{cat}</Text>
              </View>
            ))}
          </View>
        )}
      </View>

      <View style={styles.sectionRow}>
        <Text style={styles.sectionTitle}>Adatok</Text>
        <TouchableOpacity onPress={() => setEditing((e) => !e)}>
          <Ionicons name={editing ? "checkmark-outline" : "create-outline"} size={18} color="#38d9a9" style={{ marginLeft: 8 }} />
        </TouchableOpacity>
      </View>
      <View style={styles.infoBox}>
        {editing ? (
          <>
            <TextInput
              style={styles.input}
              value={profile.email}
              onChangeText={(t) => setProfile((p) => ({ ...p, email: t }))}
              placeholder="E-mail"
              placeholderTextColor="#aaa"
            />
            <TextInput
              style={styles.input}
              value={profile.age}
              onChangeText={(t) => setProfile((p) => ({ ...p, age: t }))}
              placeholder="Kor"
              placeholderTextColor="#aaa"
              keyboardType="numeric"
            />
            <TextInput
              style={styles.input}
              value={profile.gender}
              onChangeText={(t) => setProfile((p) => ({ ...p, gender: t }))}
              placeholder="Nem"
              placeholderTextColor="#aaa"
            />
            <TextInput
              style={styles.input}
              value={profile.address}
              onChangeText={(t) => setProfile((p) => ({ ...p, address: t }))}
              placeholder="Lakcím"
              placeholderTextColor="#aaa"
            />
            <TextInput
              style={styles.input}
              value={profile.company}
              onChangeText={(t) => setProfile((p) => ({ ...p, company: t }))}
              placeholder="Cégem"
              placeholderTextColor="#aaa"
            />
            <TextInput
              style={styles.input}
              value={profile.position}
              onChangeText={(t) => setProfile((p) => ({ ...p, position: t }))}
              placeholder="Pozíció"
              placeholderTextColor="#aaa"
            />
          </>
        ) : (
          <Text style={styles.infoText}>
            E-mail: {profile.email}{"\n"}
            Kor: {profile.age}{"\n"}
            Nem: {profile.gender}{"\n"}
            Lakcím: {profile.address}{"\n"}
            Cégem: {profile.company}{"\n"}
            Pozíció: {profile.position}
          </Text>
        )}
      </View>

      <TouchableOpacity style={styles.messageBox}>
        <Text style={styles.messageText}>Írj nekünk!</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0A233C",
    padding: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 18,
    marginTop: 8,
  },
  emoji: {
    fontSize: 32,
    marginRight: 10,
  },
  name: {
    fontSize: 22,
    color: "#fff",
    fontWeight: "bold",
  },
  savingsBox: {
    backgroundColor: "#162b45",
    borderRadius: 16,
    borderWidth: 2,
    borderColor: "#38d9a9",
    alignItems: "center",
    paddingVertical: 18,
    marginBottom: 16,
  },
  savingsAmount: {
    fontSize: 28,
    color: "#fff",
    fontWeight: "bold",
  },
  savingsLabel: {
    color: "#38d9a9",
    fontSize: 16,
    marginTop: 4,
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  statBox: {
    flex: 1,
    backgroundColor: "#162b45",
    borderRadius: 16,
    borderWidth: 2,
    borderColor: "#38d9a9",
    alignItems: "center",
    paddingVertical: 18,
    marginHorizontal: 4,
  },
  statNumber: {
    fontSize: 22,
    color: "#fff",
    fontWeight: "bold",
  },
  statLabel: {
    color: "#38d9a9",
    fontSize: 14,
    marginTop: 4,
    textAlign: "center",
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 8,
  },
  interestsRow: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap", // ÚJ: így több sorba törik, ha nem fér ki!
    gap: 8,           // opcionális, ha szeretnél hézagot a sorok között is
  },
  interestChip: {
    backgroundColor: "#38d9a9",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
    marginRight: 10,
  },
  interestText: {
    color: "#0A233C",
    fontWeight: "bold",
    fontSize: 14,
  },
  addChip: {
    backgroundColor: "#38d9a9",
    borderRadius: 20,
    padding: 6,
    justifyContent: "center",
    alignItems: "center",
  },
  sectionRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  infoBox: {
    backgroundColor: "#162b45",
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  infoText: {
    color: "#fff",
    fontSize: 14,
    lineHeight: 22,
  },
  messageBox: {
    backgroundColor: "#162b45",
    borderRadius: 12,
    padding: 14,
    alignItems: "center",
    marginBottom: 24,
  },
  messageText: {
    color: "#38d9a9",
    fontWeight: "bold",
    fontSize: 16,
  },
  input: {
    backgroundColor: "#22395a",
    color: "#fff",
    borderRadius: 8,
    padding: 8,
    marginBottom: 8,
    fontSize: 14,
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 8,
  },
  categoryChip: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    paddingHorizontal: 14,
    paddingVertical: 7,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryChipSelected: {
    backgroundColor: "#38d9a9",
  },
  categoryChipUnselected: {
    backgroundColor: "#fff", // fehér háttér a nem kiválasztottaknak
    borderWidth: 1,
    borderColor: "#162b45",
  },
  categoryText: {
    color: "#0A233C",
    fontWeight: "bold",
    fontSize: 14,
  },
});
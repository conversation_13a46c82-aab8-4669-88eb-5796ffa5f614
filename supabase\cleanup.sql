-- Cleanup script to remove all objects created in schema.sql

-- Drop RLS policies
drop policy if exists "Public profiles are viewable by everyone." on profiles;
drop policy if exists "Users can insert their own profile." on profiles;
drop policy if exists "Users can update own profile." on profiles;

drop policy if exists "Active discounts are viewable by everyone." on discounts;
drop policy if exists "Authenticated users can manage discounts." on discounts;

drop policy if exists "Users can view their own discount transactions." on discount_transactions;
drop policy if exists "Users can insert their own discount transactions." on discount_transactions;

drop policy if exists "Avatar images are publicly accessible." on storage.objects;
drop policy if exists "Anyone can upload an avatar." on storage.objects;
drop policy if exists "Only the owner can delete the avatar." on storage.objects;

-- Drop triggers and functions
drop trigger if exists on_profile_update on profiles;
drop function if exists public.handle_updated_at();

-- Drop tables (respecting foreign key dependencies)
drop table if exists discount_transactions;
drop table if exists discounts;
drop table if exists profiles;

-- Remove storage buckets
delete from storage.buckets where id = 'avatars'; 
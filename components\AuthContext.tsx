// C:\Users\<USER>\Desktop\asztal\BME\Startup\code\App_demo\components\AuthContext.tsx
import React, { createContext, useState, useEffect, useContext } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase'; // <-- ENSURE THIS PATH IS CORRECT FOR YOUR SUPABASE CLIENT

interface AuthContextType {
  session: Session | null;
  user: User | null;
  employeeId: string | null;
  isLoadingAuth: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [employeeId, setEmployeeId] = useState<string | null>(null);
  const [isLoadingAuth, setIsLoadingAuth] = useState(true); // Should start as true

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user || null);
      setEmployeeId(session?.user?.id || null);
      setIsLoadingAuth(false); // Should become false here!
    }).catch(error => {
      setIsLoadingAuth(false); // Set to false even on error to prevent infinite loading
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user || null);
      setEmployeeId(session?.user?.id || null);
      setIsLoadingAuth(false); // Should become false here too!
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []); // Empty dependency array means this runs once on mount

  const value = {
    session,
    user,
    employeeId,
    isLoadingAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
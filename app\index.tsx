import React from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext"; // Import your useTheme hook
import { ThemeName } from "@/constants/Themes"; // Import ThemeName if you want to explicitly use it for the buttons

export default function IndexScreen() {
  const router = useRouter();
  const { getCustomTheme, setTheme } = useTheme(); // Get the custom theme and the setTheme function
  const theme = getCustomTheme(); // Get the current theme's values

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
      <View style={styles.indexButtonContainer}>
        {/* Login Button */}
        <TouchableOpacity
          style={[
            styles.indexButton,
            { backgroundColor: theme.primary }, // Use theme.primary for the background
          ]}
          onPress={() => {
            router.replace('/profile/login/login');
          }}
        >
          <Text style={[styles.indexButtonText, { color: theme.text }]}>Login</Text>
        </TouchableOpacity>

        {/* Home Button */}
        <TouchableOpacity
          style={[
            styles.indexButton,
            { backgroundColor: theme.secondary, marginTop: 18 }, // Use theme.secondary for the background
          ]}
          onPress={() => {
            router.replace({pathname: '/home'});
          }}
        >
          <Text style={[styles.indexButtonText, { color: theme.text }]}>Home</Text>
        </TouchableOpacity>

        {/* Optional: Add buttons to switch themes directly */}
        <View style={styles.themeSwitchContainer}>
          <TouchableOpacity style={styles.themeSwitchButton} onPress={() => setTheme('blue')}>
            <Text style={styles.themeSwitchButtonText}>Blue Theme</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.themeSwitchButton} onPress={() => setTheme('cyan')}>
            <Text style={styles.themeSwitchButtonText}>Cyan Theme</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.themeSwitchButton} onPress={() => setTheme('white')}>
            <Text style={styles.themeSwitchButtonText}>White Theme</Text>
          </TouchableOpacity>
        </View>

      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  indexButtonContainer: {
    width: '80%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  indexButton: {
    width: '100%',
    paddingVertical: 22,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000', // You might want to theme shadowColor as well
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 2,
  },
  indexButtonText: {
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  themeSwitchContainer: {
    marginTop: 40,
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  themeSwitchButton: {
    padding: 10,
    backgroundColor: '#ddd', // Could also be themed
    borderRadius: 8,
  },
  themeSwitchButtonText: {
    color: '#333',
    fontWeight: 'bold',
  },
});
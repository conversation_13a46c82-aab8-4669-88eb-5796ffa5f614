import React, { createContext, useContext, useState, ReactNode } from 'react';
import { DefaultTheme, Theme as NavigationTheme } from '@react-navigation/native';
import { ThemeName, themeValues } from '@/constants/Themes';

interface ThemeContextType {
  currentThemeName: ThemeName;
  setTheme: (name: ThemeName) => void;
  getNavigationTheme: () => NavigationTheme;
  getCustomTheme: () => typeof themeValues[ThemeName];
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeContextProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentThemeName, setCurrentThemeName] = useState<ThemeName>('blue'); // Default theme

  const setTheme = (name: ThemeName) => {
    setCurrentThemeName(name);
  };

  const getCustomTheme = () => {
    return themeValues[currentThemeName];
  };

  const getNavigationTheme = (): NavigationTheme => {
    const customTheme = themeValues[currentThemeName];
    return {
      ...DefaultTheme,
      dark: currentThemeName === 'blue' || currentThemeName === 'cyan', // Example: determine dark mode based on theme
      colors: {
        ...DefaultTheme.colors,
        primary: customTheme.primary,
        background: customTheme.bg,
        card: customTheme.secondary, // You might map this differently
        text: customTheme.text,
        border: customTheme.tertiary,
      },
    };
  };

  return (
    <ThemeContext.Provider value={{ currentThemeName, setTheme, getNavigationTheme, getCustomTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeContextProvider');
  }
  return context;
};
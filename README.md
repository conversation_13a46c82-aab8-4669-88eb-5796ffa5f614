# EverDeal

A mobile application for managing and applying discount codes for shopping experiences.

## Overview

EverDeal is a React Native application built with Expo that allows users to:

- Browse available discount codes
- View discount details and expiration dates
- Track discount usage history
- Apply discounts to purchases

The app features user authentication, account management, and a clean UI for a seamless shopping experience.

## Tech Stack

- **Frontend**: React Native with Expo
- **Navigation**: Expo Router
- **State Management**: React Context API
- **Backend/Database**: Supabase
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage

## Features

- **User Authentication**: Secure sign-up and login
- **Discount Discovery**: Browse available discount codes
- **Discount History**: Track past discount usage
- **Account Management**: View and update account details

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI

### Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/EverDeal.git
cd EverDeal
```

2. Install dependencies:
```
npm install
```

3. Set up environment variables:
Create a `.env` file in the root directory with the following variables:
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Start the development server:
```
npm start
```

### Supabase Setup

The application requires a Supabase instance with the following:

1. Authentication enabled
2. Database tables as defined in `supabase/schema.sql`
3. Optional: Seed data from `supabase/seed.sql`

To set up the Supabase schema:
1. Create a new Supabase project
2. Run the SQL from `supabase/schema.sql` in the SQL editor
3. Optionally run `supabase/seed.sql` to populate with test data

## Development

### Project Structure

- `/app` - Main application screens and navigation
- `/components` - Reusable UI components
- `/lib` - Utilities and API integrations
- `/assets` - Images and other static assets
- `/constants` - App constants and configuration
- `/hooks` - Custom React hooks
- `/supabase` - Supabase schema and seed files

### Scripts

- `npm start` - Start the Expo development server
- `npm run ios` - Start the app in iOS simulator
- `npm run android` - Start the app in Android emulator
- `npm run web` - Start the app in web browser
- `npm run reset-project` - Reset the project

## License

[MIT License](LICENSE)

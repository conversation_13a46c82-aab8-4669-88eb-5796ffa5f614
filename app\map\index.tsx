import React from "react";
import { View, StyleSheet } from "react-native";
import { WebView } from "react-native-webview";

export default function MapPage() {
  return (
    <View style={styles.container}>
      <WebView
        source={{
          uri: "https://www.google.com/maps/place/47%C2%B029'53.3%22N+19%C2%B001'50.4%22E/@47.498139,19.027333,15z",
        }}
        style={styles.map}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0A233C",
    paddingTop: 16,
  },
  map: {
    flex: 1,
    borderRadius: 12,
    overflow: "hidden",
    marginHorizontal: 8,
  },
});
import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

export default function HomeHeader() {
  const router = useRouter();
  return (
    <View style={styles.header}>
      <Text style={styles.title}>EverDeal</Text>
      <TouchableOpacity
        style={styles.logoutBtn}
        onPress={() => router.replace("/profile/login/login")}
        hitSlop={10}
      >
        <Ionicons name="log-out-outline" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#021329",
    paddingTop: 18,
    paddingBottom: 12,
    paddingHorizontal: 18,
    borderBottomWidth: 1,
    borderBottomColor: "#40E0D0",
  },
  title: {
    color: "#40E0D0",
    fontSize: 22,
    fontWeight: "bold",
    letterSpacing: 1,
  },
  logoutBtn: {
    padding: 6,
    borderRadius: 16,
    backgroundColor: "#21756C",
  },
});

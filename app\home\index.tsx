import React, { useRef, useEffect, useState, useCallback } from "react";
import { StyleSheet, View, ScrollView, Image, Text, Animated, Dimensions, TouchableOpacity, Pressable, ActivityIndicator, Alert } from "react-native";
import { Easing } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import CouponsSection from "@/components/CouponsSection";
import SpecialOfferSection from "@/components/SpecialOfferSection";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/components/AuthContext';
import { useGlobalFavorites } from '@/components/GlobalFavoritesContext';

// Define the interface for story data fetched from Supabase
interface BrandData {
  brand_id: number;
  displayed_name: string;
  logo: string;
  color: string | null;
}

const { width } = Dimensions.get("window");

export default function HomePage() {
  const [dbStories, setDbStories] = useState<BrandData[]>([]);
  const [isLoadingStories, setIsLoadingStories] = useState(true);
  const [activeStory, setActiveStory] = useState<number | null>(null);
  const [showStoryOverlay, setShowStoryOverlay] = useState(false);
  const [storyOverlayLogo, setStoryOverlayLogo] = useState<string | null>(null);
  const [oldStories, setOldStories] = useState<number[]>([]);
  const [overlayTimer, setOverlayTimer] = useState(5);
  const overlayTimerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [progressAnim] = useState(() => new Animated.Value(1));
  const router = useRouter();

  // Use AuthContext to get employeeId and loading status
  const { employeeId, isLoadingAuth } = useAuth();
  // Use global favorites context
  const { userFavoriteBrandIds, toggleFavorite } = useGlobalFavorites();

  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  // Fetch stories from Supabase on component mount
  useEffect(() => {
    async function fetchStories() {
      setIsLoadingStories(true);
      try {
        const { data, error } = await supabase
          .from('brand')
          .select('brand_id, displayed_name, logo, color');

        if (error) {
          // You might want to display an error to the user or handle it silently
        } else {
          setDbStories(data || []);
        }
      } catch (err) {
        // Handle unexpected errors during fetch
      } finally {
        setIsLoadingStories(false);
      }
    }

    fetchStories();
  }, []);

  // Favorites are now managed by GlobalFavoritesContext, no need for local useEffect

  // Use the global toggle function
  const handleToggleFavorite = toggleFavorite;

  // Story overlay logic
  const showOverlay = useCallback((logo: string, initialDbIndex: number) => {
    if (overlayTimerRef.current) {
      clearInterval(overlayTimerRef.current);
      overlayTimerRef.current = null;
    }

    setStoryOverlayLogo(logo);
    setShowStoryOverlay(true);
    setOverlayTimer(5);
    setActiveStory(initialDbIndex);

    const currentStoryId = dbStories[initialDbIndex]?.brand_id;
    if (currentStoryId !== undefined && !oldStories.includes(currentStoryId)) {
      setOldStories((prev) => [...prev, currentStoryId]);
    }

    progressAnim.setValue(1);
    Animated.timing(progressAnim, {
      toValue: 0,
      duration: 5000,
      useNativeDriver: false,
      easing: Easing.linear,
    }).start();

    let currentIntervalIndex = initialDbIndex;

    overlayTimerRef.current = setInterval(() => {
      setOverlayTimer((prev) => {
        if (prev <= 1) {
          let nextIdx = currentIntervalIndex + 1;

          while (nextIdx < dbStories.length && oldStories.includes(dbStories[nextIdx].brand_id)) {
            nextIdx++;
          }

          if (nextIdx < dbStories.length) {
            const nextStory = dbStories[nextIdx];
            setStoryOverlayLogo(nextStory.logo);
            setActiveStory(nextIdx);
            setOverlayTimer(5);
            setOldStories((prev) => prev.includes(nextStory.brand_id) ? prev : [...prev, nextStory.brand_id]);
            progressAnim.setValue(1);
            Animated.timing(progressAnim, {
              toValue: 0,
              duration: 5000,
              useNativeDriver: false,
              easing: Easing.linear,
            }).start();
            currentIntervalIndex = nextIdx;
            return 5;
          } else {
            clearInterval(overlayTimerRef.current!);
            setShowStoryOverlay(false);
            return 0;
          }
        }
        return prev - 1;
      });
    }, 1000);
  }, [dbStories, oldStories, progressAnim]);

  useEffect(() => {
    return () => { if (overlayTimerRef.current) clearInterval(overlayTimerRef.current); };
  }, []);

  // Sort stories to show unread first, then read
  const sortedStories = [...dbStories].sort((a, b) => {
    const aRead = oldStories.includes(a.brand_id);
    const bRead = oldStories.includes(b.brand_id);
    if (aRead && !bRead) return 1;
    if (!aRead && bRead) return -1;
    return 0;
  });

  if (isLoadingAuth) {
    return (
      <View style={[styles.container, { backgroundColor: theme.bg, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={{ color: theme.text, marginTop: 10 }}>Loading user data...</Text>
      </View>
    );
  }

  // If isLoadingAuth is false, proceed to render the main content
  return (
    <View style={[styles.container, { backgroundColor: theme.bg }]}>
      <ScrollView>
        {/* stories */}
        <View style={[styles.storiesContainer, { backgroundColor: theme.bg }]}>
          {isLoadingStories ? (
            <ActivityIndicator size="large" color={theme.primary} style={styles.loadingIndicator} />
          ) : (
            dbStories.length > 0 ? (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {sortedStories.map((story, index) => (
                  <View key={story.brand_id} style={styles.storyCircle}>
                    <Pressable
                      onPress={() => {
                        showOverlay(story.logo, dbStories.findIndex(s => s.brand_id === story.brand_id));
                      }}
                      style={[
                        styles.storyCirclePressable,
                        { backgroundColor: story.color || '#fff' },
                        oldStories.includes(story.brand_id) && { borderColor: theme.secondary },
                        !oldStories.includes(story.brand_id) && { borderColor: theme.primary },
                      ]}
                    >
                      <Image source={{ uri: story.logo }} style={styles.storyImage} />
                    </Pressable>
                    <Text style={[styles.storyText, { color: theme.text }]}>{story.displayed_name}</Text>
                  </View>
                ))}
                <View key="reset" style={styles.storyCircle}>
                  <Pressable
                    onPress={() => {
                      setOldStories([]);
                    }}
                    style={[styles.storyCirclePressable, { borderColor: theme.primary, backgroundColor: theme.bg }]}
                  >
                    <Ionicons name="refresh" size={32} color={theme.primary} />
                  </Pressable>
                  <Text style={[styles.storyText, { color: theme.text }]}>Reset</Text>
                </View>
              </ScrollView>
            ) : (
              <Text style={{ color: theme.text, textAlign: 'center', padding: 20 }}>No stories available.</Text>
            )
          )}
        </View>

        {/* Special Offer Section - Fetches special offers from Supabase */}
        <SpecialOfferSection />

        {/* Coupons Section - Pass the favorite data and toggle function */}
        <CouponsSection
          userFavoriteBrandIds={userFavoriteBrandIds}
          onToggleFavorite={handleToggleFavorite}
        />
      </ScrollView>

      {/* Story Overlay */}
      {showStoryOverlay && (
        <View style={styles.storyOverlay}>
          <View style={[styles.overlayProgressBarBg, { backgroundColor: theme.tertiary }]}>
            <Animated.View style={[styles.overlayProgressBarFill, { width: progressAnim.interpolate({ inputRange: [0, 1], outputRange: ['0%', '100%'] }), backgroundColor: theme.primary }]} />
          </View>
          <TouchableOpacity
            style={styles.closeOverlayButton}
            onPress={() => {
              setShowStoryOverlay(false);
              if (overlayTimerRef.current) clearInterval(overlayTimerRef.current);
            }}
          >
            <Ionicons name="close-circle-outline" size={30} color={theme.text} />
          </TouchableOpacity>
          <View style={styles.storyOverlayContent}>
            <View style={{
              backgroundColor: dbStories[activeStory ?? 0]?.color || '#fff',
              borderRadius: 800,
              padding: 8,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Image source={{ uri: storyOverlayLogo || '' }} style={[styles.storyOverlayLogo, { backgroundColor: dbStories[activeStory ?? 0]?.color || '#fff' }]} />
            </View>
          </View>
          {/* Action Buttons at the bottom */}
          <View style={{
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 10,
            width: '100%',
            alignItems: 'center',
            justifyContent: 'flex-end',
            zIndex: 10,
            pointerEvents: 'box-none',
          }}>
            <TouchableOpacity
              style={{
                backgroundColor: theme.primary,
                borderRadius: 24,
                paddingVertical: 18,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 12,
                width: '90%',
                alignSelf: 'center',
              }}
              onPress={() => {
                const currentBrandId = dbStories[activeStory ?? 0]?.brand_id;
                if (currentBrandId !== undefined) {
                  router.push({ pathname: '/brand', params: { brandId: currentBrandId.toString() } });
                  setShowStoryOverlay(false);
                  if (overlayTimerRef.current) clearInterval(overlayTimerRef.current);
                }
              }}
              activeOpacity={0.85}
            >
              <Text style={{ color: theme.text, fontWeight: 'bold', fontSize: 18, marginRight: 8 }}>Mutasd</Text>
              <Ionicons name="arrow-forward" size={24} color={theme.text} />
            </TouchableOpacity>
            {/* "Mentés a kedvencekhez" button for the story overlay */}
            <TouchableOpacity
              style={{
                backgroundColor: theme.secondary,
                borderRadius: 24,
                paddingVertical: 18,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                width: '90%',
                alignSelf: 'center',
              }}
              // Use the generic handleToggleFavorite now
              onPress={async () => {
                const currentBrandId = dbStories[activeStory ?? 0]?.brand_id;
                if (currentBrandId !== undefined) {
                  await handleToggleFavorite(currentBrandId);
                }
              }}
              activeOpacity={0.85}
            >
              <Text style={{ color: theme.text, fontWeight: 'bold', fontSize: 18, marginRight: 8 }}>Mentés a kedvencekhez</Text>
              <Ionicons
                // This state check is correct as userFavoriteBrandIds is updated locally
                name={userFavoriteBrandIds.includes(dbStories[activeStory ?? 0]?.brand_id ?? -1) ? 'heart' : 'heart-outline'}
                size={24}
                color={theme.text}
              />
            </TouchableOpacity>
          </View>
          {/* Overlay navigation */}
          <Pressable
            style={{ position: 'absolute', left: 0, top: 0, bottom: 0, width: '50%', zIndex: 1 }}
            pointerEvents="box-only"
            onPress={({ nativeEvent }) => {
              if (nativeEvent.locationY < (Dimensions.get('window').height - 140)) {
                if (activeStory !== null) {
                  let prevIdx = activeStory - 1;
                  if (prevIdx >= 0) {
                    const prevStory = dbStories[prevIdx];
                    setStoryOverlayLogo(prevStory.logo);
                    setActiveStory(prevIdx);
                    setOverlayTimer(5);
                    progressAnim.setValue(1);
                    Animated.timing(progressAnim, {
                      toValue: 0,
                      duration: 5000,
                      useNativeDriver: false,
                      easing: Easing.linear,
                    }).start();
                  } else {
                    setShowStoryOverlay(false);
                    if (overlayTimerRef.current) clearInterval(overlayTimerRef.current);
                  }
                } else {
                  setShowStoryOverlay(false);
                  if (overlayTimerRef.current) clearInterval(overlayTimerRef.current);
                }
              }
            }}
          />
          <Pressable
            style={{ position: 'absolute', right: 0, top: 0, bottom: 0, width: '50%', zIndex: 1 }}
            pointerEvents="box-only"
            onPress={({ nativeEvent }) => {
              if (nativeEvent.locationY < (Dimensions.get('window').height - 140)) {
                if (activeStory !== null) {
                  let nextIdx = activeStory + 1;
                  if (nextIdx < dbStories.length) {
                    const nextStory = dbStories[nextIdx];
                    setStoryOverlayLogo(nextStory.logo);
                    setActiveStory(nextIdx);
                    setOverlayTimer(5);
                    setOldStories((prev) => prev.includes(nextStory.brand_id) ? prev : [...prev, nextStory.brand_id]);
                    progressAnim.setValue(1);
                    Animated.timing(progressAnim, {
                      toValue: 0,
                      duration: 5000,
                      useNativeDriver: false,
                      easing: Easing.linear,
                    }).start();
                  } else {
                    setShowStoryOverlay(false);
                    if (overlayTimerRef.current) clearInterval(overlayTimerRef.current);
                  }
                } else {
                  setShowStoryOverlay(false);
                  if (overlayTimerRef.current) clearInterval(overlayTimerRef.current);
                }
              }
            }}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  storiesContainer: {
    paddingTop: 20,
    paddingBottom: 16,
    paddingLeft: 16,
  },
  loadingIndicator: {
    height: 72,
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 16,
  },
  storyCircle: {
    alignItems: "center",
    marginRight: 9,
  },
  storyCirclePressable: {
    width: 72,
    height: 72,
    borderRadius: 36,
    borderWidth: 3,
    marginBottom: 4,
    alignItems: "center",
    justifyContent: "center",
  },
  storyImage: {
    width: 66,
    height: 66,
    borderRadius: 33,
    resizeMode: "contain",
    padding: 8,
  },
  storyText: {
    fontSize: 12,
    marginTop: 2,
  },
  storyOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    zIndex: 999,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeOverlayButton: {
    position: 'absolute',
    top: 30,
    right: 20,
    zIndex: 1001,
    padding: 10,
  },
  storyOverlayContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  storyOverlayLogo: {
    width: 160,
    height: 160,
    resizeMode: 'contain',
    borderRadius: 800,
    padding: 5,
  },
  overlayProgressBarBg: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    height: 8,
    zIndex: 1000,
    borderRadius: 4,
  },
  overlayProgressBarFill: {
    height: 8,
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
    borderTopLeftRadius: 4,
    borderBottomLeftRadius: 4,
  },
});
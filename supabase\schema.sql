-- Create a table for public profiles
create table profiles (
  id uuid references auth.users on delete cascade not null primary key,
  updated_at timestamp with time zone,
  username text unique,
  avatar_url text,
  website text,
  
  constraint username_length check (char_length(username) >= 3)
);

-- Set up Row Level Security (RLS)
-- This allows users to only access their own profile data
alter table profiles enable row level security;

create policy "Public profiles are viewable by everyone."
  on profiles for select
  using ( true );

create policy "Users can insert their own profile."
  on profiles for insert
  with check ( auth.uid() = id );

create policy "Users can update own profile."
  on profiles for update
  using ( auth.uid() = id );

-- Create a trigger to set updated_at whenever a profile is updated
create function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

create trigger on_profile_update
  before update on profiles
  for each row
  execute procedure handle_updated_at();

-- Create a table for discount codes
create table discounts (
  id uuid default gen_random_uuid() primary key,
  code text unique not null,
  percentage smallint not null,
  description text,
  active boolean default true,
  created_at timestamp with time zone default now(),
  expires_at timestamp with time zone,
  
  constraint valid_percentage check (percentage > 0 and percentage <= 100)
);

-- Create a table for discount transactions
create table discount_transactions (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users not null,
  discount_id uuid references discounts not null,
  applied_at timestamp with time zone default now(),
  order_amount decimal(10,2) not null,
  discount_amount decimal(10,2) not null
);

-- Set up RLS for discounts
alter table discounts enable row level security;

-- All users can view active discounts
create policy "Active discounts are viewable by everyone."
  on discounts for select
  using ( active = true );

-- Only allow admin to insert, update and delete discounts (in a real app, you'd implement admin roles)
-- For now, we allow all authenticated users to manage discounts for demo purposes
create policy "Authenticated users can manage discounts."
  on discounts for all
  using ( auth.role() = 'authenticated' );

-- Set up RLS for discount transactions
alter table discount_transactions enable row level security;

-- Users can view their own discount transactions
create policy "Users can view their own discount transactions."
  on discount_transactions for select
  using ( auth.uid() = user_id );

-- Users can insert their own discount transactions
create policy "Users can insert their own discount transactions."
  on discount_transactions for insert
  with check ( auth.uid() = user_id );

-- Set up Storage
insert into storage.buckets (id, name, public) values ('avatars', 'avatars', true);

create policy "Avatar images are publicly accessible."
  on storage.objects for select
  using ( bucket_id = 'avatars' );

create policy "Anyone can upload an avatar."
  on storage.objects for insert
  with check ( bucket_id = 'avatars' );

create policy "Only the owner can delete the avatar."
  on storage.objects for delete
  using ( auth.uid() = owner ); 
import { supabase } from '@/lib/supabase.js';

/**
 * Adds a brand to the user's favorites.
 * @param employeeId The UUID of the current authenticated user.
 * @param brandId The ID of the brand to favorite.
 * @returns true if successful, false otherwise (e.g., already favorited).
 */
export async function addFavoriteBrand(employeeId: string, brandId: number): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('employee_favorites')
      .insert({ employee_id: employeeId, brand_id: brandId });

    if (error) {
      if (error.code === '23505') { // Unique violation error code
        // Consider if you want to silently fail or throw an error for this case
        // console.warn(`Brand ${brandId} already favorited by employee ${employeeId}.`); // Keeping this warn for specific case
        return false; // Already favorited
      }
      return false;
    }
    return true;
  } catch (err) {
    return false;
  }
}

/**
 * Removes a brand from the user's favorites.
 * @param employeeId The UUID of the current authenticated user.
 * @param brandId The ID of the brand to unfavorite.
 * @returns true if successful, false otherwise.
*/
export async function removeFavoriteBrand(employeeId: string, brandId: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('employee_favorites')
      .delete()
      .eq('employee_id', employeeId)
      .eq('brand_id', brandId);

    if (error) {
      return false;
    }
    return true;
  } catch (err) {
    return false;
  }
}

/**
 * Fetches all brand_ids favorited by a specific employee.
 * @param employeeId The UUID of the current authenticated user.
 * @returns An array of favorited brand IDs.
 */
export async function getFavoriteBrandIds(employeeId: string): Promise<number[]> {
  try {
    const { data, error } = await supabase
      .from('employee_favorites')
      .select('brand_id')
      .eq('employee_id', employeeId);

    if (error) {
      return [];
    }
    return data ? data.map(item => item.brand_id) : [];
  } catch (err) {
    return [];
  }
}

/**
 * Checks if a specific brand is favorited by the employee.
 * @param employeeId The UUID of the current authenticated user.
 * @param brandId The ID of the brand to check.
 * @returns true if the brand is favorited, false otherwise.
 */
export async function isBrandFavorited(employeeId: string, brandId: number): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('employee_favorites')
      .select('id')
      .eq('employee_id', employeeId)
      .eq('brand_id', brandId)
      .limit(1); // We only need to know if at least one record exists

    if (error) {
      return false;
    }
    return data && data.length > 0;
  } catch (err) {
    return false;
  }
}